// Orders page for a specific shop branch
// URL: /[shop-type]/[shop-slug]/[branch-slug]/orders

import { notFound } from 'next/navigation';
import { ShopType, isValidShopType, getShopTypeConfig } from '@/lib/config/shop-types';
import ShopNavigation from '@/components/ShopNavigation';
import BranchOrdersComponent from '@/components/BranchOrdersPage';
import React from 'react';

interface BranchOrdersPageProps {
  params: Promise<{
    'shop-type': string;
    'shop-slug': string;
    'branch-slug': string;
  }>;
}

export default async function BranchOrdersPage({ params }: BranchOrdersPageProps) {
  const resolvedParams = await params;
  const shopType = resolvedParams['shop-type'] as ShopType;
  const shopSlug = resolvedParams['shop-slug'];
  const branchSlug = resolvedParams['branch-slug'];

  // Validate shop type
  if (!isValidShopType(shopType)) {
    notFound();
  }

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-background">
      <div className="layout-container flex h-full grow flex-col">
        {/* Shop Navigation with branch context */}
        <ShopNavigation
          shopType={shopType}
          shopSlug={shopSlug}
          branchSlug={branchSlug}
          // TODO: Fetch actual shop and branch data and pass it here
          // shopName={shop?.name}
          // branchName={branch?.name}
          // shopRating={shop?.rating}
          // shopAddress={branch?.address}
          // shopPhone={branch?.phone}
          // isOpen={branch?.is_open}
        />

        {/* Main Content */}
        <div className="px-40 flex flex-1 justify-center py-5">
          <div className="layout-content-container flex flex-col max-w-[960px] flex-1">
            {/* Branch-specific orders component with actual data */}
            <BranchOrdersComponent
              shopSlug={shopSlug}
              branchSlug={branchSlug}
              shopType={shopType}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

// Generate metadata for the page
export async function generateMetadata({ params }: BranchOrdersPageProps) {
  const resolvedParams = await params;
  const shopType = resolvedParams['shop-type'] as ShopType;
  const shopSlug = resolvedParams['shop-slug'];
  const branchSlug = resolvedParams['branch-slug'];

  if (!isValidShopType(shopType)) {
    return {
      title: 'Orders - Shop Not Found',
      description: 'The requested shop could not be found.',
    };
  }

  const config = getShopTypeConfig(shopType);
  const shopName = shopSlug.replace(/-/g, ' ');
  const branchName = branchSlug.replace(/-/g, ' ');

  return {
    title: `Orders - ${shopName} (${branchName}) | ${config.label}`,
    description: `View your orders from ${shopName} - ${branchName} branch.`,
  };
}
