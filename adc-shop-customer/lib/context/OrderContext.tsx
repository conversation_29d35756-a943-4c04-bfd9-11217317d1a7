"use client";

import React, { createContext, useContext, useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { CartItem } from './CartContext';
import {
  useGetOrdersByCustomerQuery,
  useCreateOrderWithPaymentMutation
} from '@/lib/store/api/customerApi';
import { sessionService } from '@/lib/services/sessionService';

export interface Order {
  id: string;
  status: 'preparing' | 'ready' | 'delivered' | 'cancelled';
  items: Array<{
    name: string;
    quantity: number;
    price: number;
  }>;
  total: number;
  orderDate: Date;
  estimatedTime?: string;
  image: string;
  paymentStatus?: 'pending' | 'paid' | 'failed' | 'refunded';
  paymentMethod?: string;
  paymentIntentId?: string;
}

interface OrderContextType {
  currentOrders: Order[];
  orderHistory: Order[];
  createOrder: (cartItems: CartItem[], total: number, paymentInfo?: {
    paymentStatus?: 'pending' | 'paid' | 'failed' | 'refunded';
    paymentMethod?: string;
    paymentIntentId?: string;
  }) => string;
  updateOrderStatus: (orderId: string, status: Order['status']) => void;
}

const OrderContext = createContext<OrderContextType | undefined>(undefined);

export function OrderProvider({ children }: { children: React.ReactNode }) {
  const { data: session } = useSession();
  const [createOrderWithPayment] = useCreateOrderWithPaymentMutation();
  const [sessionId, setSessionId] = useState<string | null>(null);

  // Initialize session ID on client side only
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const id = sessionService.getSessionId();
      setSessionId(id);
    }
  }, []);

  // Get customer identifier - email for authenticated users, session ID for guests
  const customerIdentifier = session?.user?.email || sessionId;

  // Get orders data from API
  const {
    data: ordersResponse,
    refetch: refetchOrders
  } = useGetOrdersByCustomerQuery({
    customerPhone: customerIdentifier, // Use email for auth users, session ID for guests
    limit: 50,
  }, {
    skip: !customerIdentifier,
  });

  const orders = ordersResponse?.data?.orders || [];

  // Separate current orders and history based on status
  const currentOrders = orders.filter((order: any) =>
    ['pending', 'preparing', 'ready'].includes(order.status)
  );
  const orderHistory = orders.filter((order: any) =>
    ['delivered', 'cancelled'].includes(order.status)
  );

  const createOrder = async (cartItems: CartItem[], total: number, orderDetails: {
    customerName: string;
    customerPhone: string;
    customerEmail?: string;
    tableId?: string;
    orderType: 'dine_in' | 'takeaway' | 'delivery';
    paymentMethod: 'card' | 'cash' | 'stripe_connect';
    notes?: string;
    branchId: string;
  }): Promise<{
    orderId: string;
    paymentIntent?: {
      clientSecret: string;
      paymentIntentId: string;
      connectedAccountId: string;
    };
    requiresPayment: boolean;
  }> => {
    try {
      // Prepare order items for API
      const orderItems = cartItems.map(item => ({
        menu_item_id: item.id,
        quantity: item.quantity,
        unit_price: item.price,
        customizations: item.customizations || [],
        special_requests: item.notes || ''
      }));

      // For guest users, use session ID as customer phone for consistency
      const customerPhone = session?.user ? orderDetails.customerPhone : sessionId;

      // Create order with payment via RTK Query mutation
      const result = await createOrderWithPayment({
        branch_id: orderDetails.branchId,
        table_id: orderDetails.tableId,
        customer_name: orderDetails.customerName,
        customer_phone: customerPhone || orderDetails.customerPhone,
        customer_email: orderDetails.customerEmail,
        order_type: orderDetails.orderType,
        items: orderItems,
        notes: orderDetails.notes,
        payment_method: orderDetails.paymentMethod,
        metadata: {
          created_via: 'customer_app',
          total_items: cartItems.length.toString(),
          user_type: session?.user ? 'authenticated' : 'guest',
          session_id: sessionId || '',
        }
      }).unwrap();

      const orderData = result.data;

      // Refetch orders to get the latest data
      refetchOrders();

      // Return order creation result
      return {
        orderId: orderData.order.id,
        paymentIntent: orderData.payment_intent ? {
          clientSecret: orderData.payment_intent.client_secret,
          paymentIntentId: orderData.payment_intent.payment_intent_id,
          connectedAccountId: orderData.connected_account_id,
        } : undefined,
        requiresPayment: orderData.requires_payment,
      };
    } catch (error) {
      console.error('Failed to create order:', error);
      throw error;
    }
  };

  const updateOrderStatus = (orderId: string, status: Order['status']) => {
    // Since we're using RTK Query, we'll refetch the orders after status update
    // The actual status update should be handled by the backend
    refetchOrders();
  };

  const value = {
    currentOrders: currentOrders.map((order: any) => ({
      id: order.id,
      status: order.status,
      items: order.order_items?.map((item: any) => ({
        name: item.name,
        quantity: item.quantity,
        price: item.unit_price || item.price,
      })) || [],
      total: order.total,
      orderDate: new Date(order.created_at),
      estimatedTime: order.estimated_time ? `${order.estimated_time} mins` : "15-20 mins",
      image: "https://images.unsplash.com/photo-**********-d2229ba7433a?w=400&h=300&fit=crop",
      paymentStatus: order.payment_status,
      paymentMethod: order.payment_method,
      paymentIntentId: order.payment_intent_id,
    })),
    orderHistory: orderHistory.map((order: any) => ({
      id: order.id,
      status: order.status,
      items: order.order_items?.map((item: any) => ({
        name: item.name,
        quantity: item.quantity,
        price: item.unit_price || item.price,
      })) || [],
      total: order.total,
      orderDate: new Date(order.created_at),
      estimatedTime: order.estimated_time ? `${order.estimated_time} mins` : "15-20 mins",
      image: "https://images.unsplash.com/photo-1467003909585-2f8a72700288?w=400&h=300&fit=crop",
      paymentStatus: order.payment_status,
      paymentMethod: order.payment_method,
      paymentIntentId: order.payment_intent_id,
    })),
    createOrder,
    updateOrderStatus
  };

  return (
    <OrderContext.Provider value={value}>
      {children}
    </OrderContext.Provider>
  );
}

export function useOrders() {
  const context = useContext(OrderContext);
  if (context === undefined) {
    throw new Error('useOrders must be used within an OrderProvider');
  }
  return context;
}
