<!DOCTYPE html>
<html>
<head>
    <title>Test Session ID</title>
</head>
<body>
    <h1>Session ID Test</h1>
    <p>Current session ID: <span id="current-session"></span></p>
    <p>Target session ID: guest_1749100308081_x4tt0ris2</p>
    
    <button onclick="updateSession()">Update to Target Session ID</button>
    <button onclick="clearSession()">Clear Session</button>
    <button onclick="checkOrders()">Check Orders</button>
    
    <div id="result"></div>

    <script>
        function getCurrentSession() {
            return localStorage.getItem('cart_session_id');
        }
        
        function updateSession() {
            localStorage.setItem('cart_session_id', 'guest_1749100308081_x4tt0ris2');
            document.getElementById('current-session').textContent = getCurrentSession();
            document.getElementById('result').innerHTML = '<p style="color: green;">Session updated!</p>';
        }
        
        function clearSession() {
            localStorage.removeItem('cart_session_id');
            document.getElementById('current-session').textContent = getCurrentSession() || 'None';
            document.getElementById('result').innerHTML = '<p style="color: orange;">Session cleared!</p>';
        }
        
        async function checkOrders() {
            const sessionId = getCurrentSession();
            try {
                const response = await fetch(`/api/orders/customer?customerPhone=${sessionId}&limit=50`);
                const data = await response.json();
                document.getElementById('result').innerHTML = `
                    <h3>Orders for session ${sessionId}:</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                document.getElementById('result').innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
            }
        }
        
        // Initialize
        document.getElementById('current-session').textContent = getCurrentSession() || 'None';
    </script>
</body>
</html>
