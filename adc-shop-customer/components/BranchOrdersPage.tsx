"use client";

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { ChevronLeft, ChevronRight, Clock, CheckCircle, XCircle, Package, CreditCard, Loader2 } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  useGetOrdersByCustomerQuery,
  useGetShopBySlugQuery,
  useGetShopBranchBySlugQuery
} from "@/lib/store/api/customerApi";
import { sessionService } from "@/lib/services/sessionService";

interface BranchOrdersPageProps {
  shopSlug: string;
  branchSlug: string;
  shopType: string;
}

interface Order {
  id: string;
  order_number: string;
  status: 'pending' | 'preparing' | 'ready' | 'delivered' | 'cancelled';
  order_items: Array<{
    name: string;
    quantity: number;
    unit_price: number;
    total: number;
  }>;
  total: number;
  created_at: string;
  updated_at: string;
  estimated_time?: number;
  payment_status?: 'pending' | 'paid' | 'failed' | 'refunded';
  payment_method?: string;
  branch?: {
    id: string;
    name: string;
    slug: string;
  };
  customer_name?: string;
  customer_phone?: string;
}

export default function BranchOrdersPage({ shopSlug, branchSlug, shopType }: BranchOrdersPageProps) {
  const { data: session } = useSession();
  const searchParams = useSearchParams();
  const [showPaymentSuccess, setShowPaymentSuccess] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);

  // Get shop and branch data
  const { data: shopResponse, isLoading: shopLoading } = useGetShopBySlugQuery(shopSlug);
  const { data: branchResponse, isLoading: branchLoading } = useGetShopBranchBySlugQuery({
    shopSlug,
    branchSlug,
  });

  // Initialize session ID on client side only
  useEffect(() => {
    if (typeof window !== 'undefined') {
      console.log('BranchOrdersPage: Initializing session ID...');
      const id = sessionService.getOrCreateSessionId(); // Use getOrCreateSessionId to ensure we have one
      console.log('BranchOrdersPage: Session ID from service:', id);
      setSessionId(id);
    }
  }, []);

  // Get orders data - use email for authenticated users, session ID for guests
  const customerIdentifier = session?.user?.email || sessionId;

  const {
    data: ordersResponse,
    isLoading: ordersLoading,
    error: ordersError,
    refetch: refetchOrders
  } = useGetOrdersByCustomerQuery({
    customerPhone: customerIdentifier, // Use email for auth users, session ID for guests
    branchId: branchResponse?.data?.branch?.id,
    limit: 50,
  }, {
    skip: !customerIdentifier, // Only skip if no customer identifier
  });

  const shop = shopResponse?.data?.shop;
  const branch = branchResponse?.data?.branch;
  const orders = ordersResponse?.data?.orders || [];

  // Debug logging
  console.log('Debug Orders Page:', {
    sessionId,
    customerIdentifier,
    session: session?.user,
    ordersResponse,
    orders,
    branchId: branchResponse?.data?.branch?.id,
    querySkipped: !customerIdentifier,
    ordersLoading,
    ordersError
  });

  // Separate current orders and history
  const currentOrders = orders.filter((order: Order) =>
    ['pending', 'preparing', 'ready'].includes(order.status)
  );
  const orderHistory = orders.filter((order: Order) =>
    ['delivered', 'cancelled'].includes(order.status)
  );

  useEffect(() => {
    if (searchParams.get('payment') === 'success') {
      setShowPaymentSuccess(true);
      // Refetch orders to get the latest data
      refetchOrders();
      // Hide the success message after 5 seconds
      const timer = setTimeout(() => {
        setShowPaymentSuccess(false);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [searchParams, refetchOrders]);

  const getStatusIcon = (status: Order['status']) => {
    switch (status) {
      case 'pending':
      case 'preparing':
        return <Clock className="h-4 w-4" />;
      case 'ready':
        return <Package className="h-4 w-4" />;
      case 'delivered':
        return <CheckCircle className="h-4 w-4" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'pending':
      case 'preparing':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'ready':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'delivered':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
    }
  };

  const getStatusText = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return 'Order Placed';
      case 'preparing':
        return 'Preparing';
      case 'ready':
        return 'Ready for Pickup';
      case 'delivered':
        return 'Delivered';
      case 'cancelled':
        return 'Cancelled';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getEstimatedTime = (order: Order) => {
    if (order.estimated_time) {
      return `${order.estimated_time} mins`;
    }
    if (order.status === 'ready') {
      return 'Ready for pickup';
    }
    return '15-20 mins';
  };

  // Loading state
  if (shopLoading || branchLoading || ordersLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading orders...</span>
      </div>
    );
  }

  // Error state
  if (ordersError) {
    return (
      <div className="p-4">
        <Alert variant="destructive">
          <AlertDescription>
            Failed to load orders. Please try again later.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // No customer identifier (shouldn't happen, but handle gracefully)
  if (!customerIdentifier) {
    return (
      <div className="p-4">
        <Alert>
          <AlertDescription>
            Unable to load orders. Please refresh the page or sign in to view your order history.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="flex flex-col px-4 py-6">
      <div className="flex flex-wrap justify-between gap-3 p-4">
        <div>
          <p className="text-foreground tracking-light text-[32px] font-bold leading-tight min-w-72">
            Your Orders
          </p>
          {shop && branch && (
            <p className="text-muted-foreground text-lg">
              {shop.name} - {branch.name}
            </p>
          )}

          {/* Debug Info */}
          <div className="mt-4 p-3 bg-gray-100 rounded text-sm">
            <p><strong>Debug Info:</strong></p>
            <p>Session ID: {sessionId || 'None'}</p>
            <p>Customer Identifier: {customerIdentifier || 'None'}</p>
            <p>User: {session?.user ? 'Authenticated' : 'Guest'}</p>
            <p>Orders Count: {orders.length}</p>
            <p>Branch ID: {branchResponse?.data?.branch?.id || 'None'}</p>
            <p>API Loading: {ordersLoading ? 'Yes' : 'No'}</p>
            <p>API Error: {ordersError ? 'Yes' : 'No'}</p>
            <div className="mt-2">
              <button
                onClick={() => {
                  localStorage.setItem('cart_session_id', 'guest_1749100308081_x4tt0ris2');
                  window.location.reload();
                }}
                className="px-3 py-1 bg-blue-500 text-white rounded text-xs mr-2"
              >
                Set Test Session ID
              </button>
              <button
                onClick={() => {
                  localStorage.removeItem('cart_session_id');
                  window.location.reload();
                }}
                className="px-3 py-1 bg-red-500 text-white rounded text-xs"
              >
                Clear Session
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Success Notification */}
      {showPaymentSuccess && (
        <div className="mx-4 mb-6">
          <Card className="bg-green-50 border-green-200">
            <CardContent className="flex items-center gap-3 p-4">
              <CheckCircle className="h-6 w-6 text-green-600" />
              <div>
                <p className="text-green-800 font-medium">Payment Successful!</p>
                <p className="text-green-700 text-sm">Your order has been placed and payment has been processed.</p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Tab Navigation */}
      <Tabs defaultValue="current" className="pb-3">
        <TabsList className="grid w-full grid-cols-2 px-4">
          <TabsTrigger value="current">Current Orders ({currentOrders.length})</TabsTrigger>
          <TabsTrigger value="history">Order History ({orderHistory.length})</TabsTrigger>
        </TabsList>

        {/* Current Orders Tab */}
        <TabsContent value="current">
          <div className="flex flex-col px-4 py-6">
            {currentOrders.length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center gap-6 p-8">
                  <div
                    className="bg-center bg-no-repeat aspect-video bg-cover rounded-xl w-full max-w-[360px]"
                    style={{
                      backgroundImage: `url("https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop")`
                    }}
                  />
                  <div className="flex max-w-[480px] flex-col items-center gap-2">
                    <p className="text-foreground text-lg font-bold leading-tight tracking-[-0.015em] max-w-[480px] text-center">
                      No current orders
                    </p>
                    <p className="text-foreground text-sm font-normal leading-normal max-w-[480px] text-center">
                      You haven&apos;t placed any orders yet. Browse the menu and start ordering!
                    </p>
                    <Button className="mt-4" asChild>
                      <a href={`/${shopType}/${shopSlug}/${branchSlug}`}>Browse Menu</a>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {currentOrders.map((order: Order) => (
                  <Card key={order.id}>
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <p className="text-muted-foreground text-sm font-normal">Order {order.order_number}</p>
                            <Badge className={`flex items-center gap-1 ${getStatusColor(order.status)}`}>
                              {getStatusIcon(order.status)}
                              {getStatusText(order.status)}
                            </Badge>
                          </div>
                          <CardTitle className="text-lg mb-1">{getStatusText(order.status)}</CardTitle>
                          <p className="text-muted-foreground text-sm">
                            {order.order_items?.length || 0} items · ${order.total?.toFixed(2) || '0.00'}
                          </p>
                          <p className="text-primary text-sm font-medium mt-1">
                            {getEstimatedTime(order)}
                          </p>
                        </div>
                        <div
                          className="w-20 h-20 bg-center bg-no-repeat bg-cover rounded-lg"
                          style={{ 
                            backgroundImage: `url("https://images.unsplash.com/photo-1553979459-d2229ba7433a?w=400&h=300&fit=crop")` 
                          }}
                        />
                      </div>
                    </CardHeader>
                    <CardContent>
                      <Separator className="mb-4" />
                      <h4 className="text-foreground font-medium mb-2">Order Items:</h4>
                      <div className="space-y-1">
                        {order.order_items?.map((item, index) => (
                          <div key={index} className="flex justify-between text-sm">
                            <span className="text-muted-foreground">
                              {item.quantity}x {item.name}
                            </span>
                            <span className="text-foreground">
                              ${item.total?.toFixed(2) || '0.00'}
                            </span>
                          </div>
                        ))}
                      </div>

                      {/* Payment Status */}
                      {order.payment_status && (
                        <div className="mt-3 pt-3 border-t">
                          <div className="flex items-center gap-2">
                            <CreditCard className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm text-muted-foreground">Payment:</span>
                            <Badge
                              className={`text-xs ${
                                order.payment_status === 'paid'
                                  ? 'bg-green-100 text-green-800 border-green-200'
                                  : order.payment_status === 'pending'
                                  ? 'bg-yellow-100 text-yellow-800 border-yellow-200'
                                  : 'bg-red-100 text-red-800 border-red-200'
                              }`}
                            >
                              {order.payment_status === 'paid' ? 'Paid' :
                               order.payment_status === 'pending' ? 'Pending' :
                               order.payment_status === 'failed' ? 'Failed' : 'Refunded'}
                            </Badge>
                            {order.payment_method && (
                              <span className="text-xs text-muted-foreground">
                                via {order.payment_method}
                              </span>
                            )}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </TabsContent>

        {/* Order History Tab */}
        <TabsContent value="history">
          <div className="flex flex-col">
            <div className="px-4 py-6">
              <h2 className="text-foreground text-[22px] font-bold leading-tight tracking-[-0.015em] pb-3">
                Past Orders
              </h2>

              {orderHistory.length === 0 ? (
                <Card>
                  <CardContent className="flex flex-col items-center gap-6 p-8">
                    <p className="text-muted-foreground text-center">No order history found.</p>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-4">
                  {orderHistory.map((order: Order) => (
                    <Card key={order.id}>
                      <CardContent className="flex items-stretch justify-between gap-4 p-4">
                        <div className="flex flex-col gap-1 flex-[2_2_0px]">
                          <div className="flex items-center gap-3 mb-1">
                            <p className="text-muted-foreground text-sm font-normal">Order {order.order_number}</p>
                            <Badge className={`flex items-center gap-1 ${getStatusColor(order.status)}`}>
                              {getStatusIcon(order.status)}
                              {getStatusText(order.status)}
                            </Badge>
                          </div>
                          <p className="text-foreground text-base font-bold">{getStatusText(order.status)}</p>
                          <p className="text-muted-foreground text-sm">
                            {order.order_items?.length || 0} items · ${order.total?.toFixed(2) || '0.00'}
                          </p>
                          <p className="text-muted-foreground text-xs">
                            {formatDate(order.created_at)}
                          </p>
                        </div>
                        <div
                          className="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-xl flex-1"
                          style={{ 
                            backgroundImage: `url("https://images.unsplash.com/photo-1467003909585-2f8a72700288?w=400&h=300&fit=crop")` 
                          }}
                        />
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>

            {/* Pagination - TODO: Implement actual pagination */}
            {orderHistory.length > 0 && (
              <Card className="mx-4">
                <CardContent className="flex items-center justify-center p-4">
                  <div className="flex items-center gap-1">
                    <Button variant="ghost" size="sm" className="h-10 w-10 p-0">
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" className="h-10 w-10 p-0 bg-muted text-foreground font-bold">
                      1
                    </Button>
                    <Button variant="ghost" size="sm" className="h-10 w-10 p-0">
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
