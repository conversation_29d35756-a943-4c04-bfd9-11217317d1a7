package config

import (
	"fmt"
	"os"
	"strconv"
)

// Config holds all configuration for the customer service
type Config struct {
	Server   ServerConfig   `json:"server"`
	Database DatabaseConfig `json:"database"`
	Log      LogConfig      `json:"log"`
	Customer CustomerConfig `json:"customer"`
	Stripe   StripeConfig   `json:"stripe"`
	JWT      JWTConfig      `json:"jwt"`
}

// ServerConfig holds server configuration
type ServerConfig struct {
	Port    string `json:"port"`
	GinMode string `json:"gin_mode"`
	Host    string `json:"host"`
}

// DatabaseConfig holds database configuration
type DatabaseConfig struct {
	Host     string `json:"host"`
	Port     string `json:"port"`
	User     string `json:"user"`
	Password string `json:"password"`
	DBName   string `json:"db_name"`
	SSLMode  string `json:"ssl_mode"`
}

// LogConfig holds logging configuration
type LogConfig struct {
	Level  string `json:"level"`
	Format string `json:"format"`
}

// CustomerConfig holds customer-specific configuration
type CustomerConfig struct {
	DefaultPageSize    int  `json:"default_page_size"`
	MaxPageSize        int  `json:"max_page_size"`
	CacheTimeout       int  `json:"cache_timeout"`
	EnableCenterPaging bool `json:"enable_center_paging"`
	ShopSettingsCache  int  `json:"shop_settings_cache"`
}

// StripeConfig holds Stripe payment configuration
type StripeConfig struct {
	SecretKey      string `json:"secret_key"`
	PublishableKey string `json:"publishable_key"`
	WebhookSecret  string `json:"webhook_secret"`
	PlatformFee    int64  `json:"platform_fee"` // Platform fee in basis points (e.g., 250 = 2.5%)
	Currency       string `json:"currency"`
}

// JWTConfig holds JWT authentication configuration
type JWTConfig struct {
	Secret    string `json:"secret"`
	ExpiresIn int    `json:"expires_in"` // in hours
}

// LoadConfig loads configuration from environment variables
func LoadConfig() (*Config, error) {
	config := &Config{
		Server: ServerConfig{
			Port:    getEnv("CUSTOMER_PORT", "8900"),
			GinMode: getEnv("GIN_MODE", "debug"),
			Host:    getEnv("CUSTOMER_HOST", "localhost"),
		},
		Database: DatabaseConfig{
			Host:     getEnv("DB_HOST", "localhost"),
			Port:     getEnv("DB_PORT", "5432"),
			User:     getEnv("DB_USER", "postgres"),
			Password: getEnv("DB_PASSWORD", ""),
			DBName:   getEnv("DB_NAME", "restaurant_db"),
			SSLMode:  getEnv("DB_SSL_MODE", "disable"),
		},
		Log: LogConfig{
			Level:  getEnv("LOG_LEVEL", "info"),
			Format: getEnv("LOG_FORMAT", "json"),
		},
		Customer: CustomerConfig{
			DefaultPageSize:    getEnvAsInt("CUSTOMER_DEFAULT_PAGE_SIZE", 20),
			MaxPageSize:        getEnvAsInt("CUSTOMER_MAX_PAGE_SIZE", 100),
			CacheTimeout:       getEnvAsInt("CUSTOMER_CACHE_TIMEOUT", 300),
			EnableCenterPaging: getEnvAsBool("CUSTOMER_ENABLE_CENTER_PAGING", true),
			ShopSettingsCache:  getEnvAsInt("CUSTOMER_SHOP_SETTINGS_CACHE", 600),
		},
		Stripe: StripeConfig{
			SecretKey:      getEnv("STRIPE_SECRET_KEY", ""),
			PublishableKey: getEnv("STRIPE_PUBLISHABLE_KEY", ""),
			WebhookSecret:  getEnv("STRIPE_WEBHOOK_SECRET", ""),
			PlatformFee:    int64(getEnvAsInt("STRIPE_PLATFORM_FEE", 250)), // 2.5% default
			Currency:       getEnv("STRIPE_CURRENCY", "thb"),
		},
		JWT: JWTConfig{
			Secret:    getEnv("JWT_SECRET", "customer-jwt-secret-key"),
			ExpiresIn: getEnvAsInt("JWT_EXPIRES_IN", 24), // 24 hours default
		},
	}

	return config, nil
}

// GetDSN returns the database connection string
func (c *Config) GetDSN() string {
	return fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		c.Database.Host,
		c.Database.Port,
		c.Database.User,
		c.Database.Password,
		c.Database.DBName,
		c.Database.SSLMode,
	)
}

// Helper functions
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvAsBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}
