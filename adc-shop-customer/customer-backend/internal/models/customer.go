package models

import (
	"time"

	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// Customer represents a customer user in the system
type Customer struct {
	BaseModel
	Email     string `json:"email" gorm:"type:varchar(255);uniqueIndex;not null"`
	FirstName string `json:"first_name" gorm:"type:varchar(100)"`
	LastName  string `json:"last_name" gorm:"type:varchar(100)"`
	Phone     string `json:"phone" gorm:"type:varchar(50)"`
	Password  string `json:"-" gorm:"type:varchar(255);not null"` // Hidden from JSON

	// Profile information
	DateOfBirth *time.Time `json:"date_of_birth,omitempty"`
	Gender      string     `json:"gender" gorm:"type:varchar(20)"`
	AvatarURL   string     `json:"avatar_url" gorm:"type:varchar(500)"`

	// Address information
	Address    string  `json:"address" gorm:"type:text"`
	City       string  `json:"city" gorm:"type:varchar(100)"`
	State      string  `json:"state" gorm:"type:varchar(100)"`
	PostalCode string  `json:"postal_code" gorm:"type:varchar(20)"`
	Country    string  `json:"country" gorm:"type:varchar(100);default:'Thailand'"`
	Latitude   float64 `json:"latitude,omitempty"`
	Longitude  float64 `json:"longitude,omitempty"`

	// Preferences
	PreferredLanguage string `json:"preferred_language" gorm:"type:varchar(10);default:'en'"`
	Timezone          string `json:"timezone" gorm:"type:varchar(50);default:'Asia/Bangkok'"`
	NotificationPrefs string `json:"notification_preferences" gorm:"type:jsonb"`

	// OAuth information
	Provider   string `json:"provider,omitempty" gorm:"type:varchar(50)"` // google, facebook, etc.
	ProviderID string `json:"provider_id,omitempty" gorm:"type:varchar(255)"`

	// Account status
	IsEmailVerified bool      `json:"is_email_verified" gorm:"default:false"`
	IsPhoneVerified bool      `json:"is_phone_verified" gorm:"default:false"`
	IsActive        bool      `json:"is_active" gorm:"default:true"`
	LastLoginAt     time.Time `json:"last_login_at,omitempty"`

	// Relationships
	Orders []Order `json:"orders,omitempty" gorm:"foreignKey:CustomerID"`
}

// SetPassword hashes and sets the customer's password
func (c *Customer) SetPassword(password string) error {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	c.Password = string(hashedPassword)
	return nil
}

// CheckPassword verifies if the provided password matches the customer's password
func (c *Customer) CheckPassword(password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(c.Password), []byte(password))
	return err == nil
}

// GetFullName returns the customer's full name
func (c *Customer) GetFullName() string {
	if c.FirstName == "" && c.LastName == "" {
		return c.Email
	}
	return c.FirstName + " " + c.LastName
}

// BeforeCreate hook to set UUID and hash password if needed
func (c *Customer) BeforeCreate(tx *gorm.DB) error {
	if c.ID == uuid.Nil {
		c.ID = uuid.New()
	}
	return nil
}

// TableName returns the table name for the Customer model
func (Customer) TableName() string {
	return "customers"
}
