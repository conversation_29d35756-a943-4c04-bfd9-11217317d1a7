package services

import (
	"context"
	"fmt"
	"time"

	"customer-backend/internal/models"
	"customer-backend/internal/repositories"
	"customer-backend/internal/types"
	"customer-backend/pkg/logger"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
)

// AuthService handles customer authentication business logic
type AuthService struct {
	customerRepo repositories.CustomerRepository
	logger       *logger.Logger
	secretKey    string
	expiresIn    time.Duration
}

// NewAuthService creates a new authentication service
func NewAuthService(customerRepo repositories.CustomerRepository, logger *logger.Logger, secretKey string) *AuthService {
	return &AuthService{
		customerRepo: customerRepo,
		logger:       logger,
		secretKey:    secretKey,
		expiresIn:    24 * time.Hour, // 24 hours
	}
}

// Login authenticates a customer and returns a JWT token
func (s *AuthService) Login(ctx context.Context, req types.LoginRequest) (*types.LoginResponse, error) {
	// Find customer by email
	customer, err := s.customerRepo.GetByEmail(ctx, req.Email)
	if err != nil {
		s.logger.WithError(err).Error("Customer not found for login")
		return nil, fmt.Errorf("invalid credentials")
	}

	// Verify password
	if !customer.CheckPassword(req.Password) {
		s.logger.Error("Invalid password for customer login")
		return nil, fmt.Errorf("invalid credentials")
	}

	// Update last login time
	customer.LastLoginAt = time.Now()
	if err := s.customerRepo.Update(ctx, customer); err != nil {
		s.logger.WithError(err).Error("Failed to update last login time")
		// Don't fail login for this
	}

	// Generate JWT token
	token, err := s.generateToken(customer)
	if err != nil {
		s.logger.WithError(err).Error("Failed to generate token")
		return nil, fmt.Errorf("failed to generate token")
	}

	return &types.LoginResponse{
		Success: true,
		Message: "Login successful",
		Data: types.LoginData{
			Token: token,
			User: types.CustomerData{
				ID:        customer.ID.String(),
				Email:     customer.Email,
				FirstName: customer.FirstName,
				LastName:  customer.LastName,
				Name:      customer.GetFullName(),
				Phone:     customer.Phone,
				Role:      "customer",
			},
			ExpiresAt: time.Now().Add(s.expiresIn),
		},
	}, nil
}

// Register creates a new customer account
func (s *AuthService) Register(ctx context.Context, req types.RegisterRequest) (*types.RegisterResponse, error) {
	// Check if customer already exists
	existingCustomer, err := s.customerRepo.GetByEmail(ctx, req.Email)
	if err == nil && existingCustomer != nil {
		return nil, fmt.Errorf("customer with this email already exists")
	}

	// Create new customer
	customer := &models.Customer{
		Email:     req.Email,
		FirstName: req.FirstName,
		LastName:  req.LastName,
		Phone:     req.Phone,
		IsActive:  true,
	}

	// Set password
	if err := customer.SetPassword(req.Password); err != nil {
		s.logger.WithError(err).Error("Failed to hash password")
		return nil, fmt.Errorf("failed to create account")
	}

	// Save customer to database
	if err := s.customerRepo.Create(ctx, customer); err != nil {
		s.logger.WithError(err).Error("Failed to create customer")
		return nil, fmt.Errorf("failed to create account")
	}

	return &types.RegisterResponse{
		Success: true,
		Message: "Account created successfully",
		Data: types.CustomerData{
			ID:        customer.ID.String(),
			Email:     customer.Email,
			FirstName: customer.FirstName,
			LastName:  customer.LastName,
			Name:      customer.GetFullName(),
			Phone:     customer.Phone,
			Role:      "customer",
		},
	}, nil
}

// CreateOAuthCustomer creates or updates a customer from OAuth provider
func (s *AuthService) CreateOAuthCustomer(ctx context.Context, req types.OAuthCustomerRequest) (*types.LoginResponse, error) {
	// Check if customer exists by email
	customer, err := s.customerRepo.GetByEmail(ctx, req.Email)
	if err != nil {
		// Customer doesn't exist, create new one
		customer = &models.Customer{
			Email:           req.Email,
			FirstName:       req.FirstName,
			LastName:        req.LastName,
			Provider:        req.Provider,
			ProviderID:      req.ProviderID,
			IsActive:        true,
			IsEmailVerified: true, // OAuth emails are considered verified
		}

		// Set a default password (won't be used for OAuth login)
		if err := customer.SetPassword("oauth-default-password"); err != nil {
			s.logger.WithError(err).Error("Failed to set default password for OAuth customer")
			return nil, fmt.Errorf("failed to create account")
		}

		if err := s.customerRepo.Create(ctx, customer); err != nil {
			s.logger.WithError(err).Error("Failed to create OAuth customer")
			return nil, fmt.Errorf("failed to create account")
		}
	} else {
		// Customer exists, update OAuth info if needed
		if customer.Provider == "" {
			customer.Provider = req.Provider
			customer.ProviderID = req.ProviderID
			customer.IsEmailVerified = true
			if err := s.customerRepo.Update(ctx, customer); err != nil {
				s.logger.WithError(err).Error("Failed to update OAuth customer")
			}
		}
	}

	// Update last login time
	customer.LastLoginAt = time.Now()
	if err := s.customerRepo.Update(ctx, customer); err != nil {
		s.logger.WithError(err).Error("Failed to update last login time for OAuth customer")
	}

	// Generate JWT token
	token, err := s.generateToken(customer)
	if err != nil {
		s.logger.WithError(err).Error("Failed to generate token for OAuth customer")
		return nil, fmt.Errorf("failed to generate token")
	}

	return &types.LoginResponse{
		Success: true,
		Message: "OAuth login successful",
		Data: types.LoginData{
			Token: token,
			User: types.CustomerData{
				ID:        customer.ID.String(),
				Email:     customer.Email,
				FirstName: customer.FirstName,
				LastName:  customer.LastName,
				Name:      customer.GetFullName(),
				Phone:     customer.Phone,
				Role:      "customer",
			},
			ExpiresAt: time.Now().Add(s.expiresIn),
		},
	}, nil
}

// generateToken creates a JWT token for the customer
func (s *AuthService) generateToken(customer *models.Customer) (string, error) {
	claims := jwt.MapClaims{
		"sub":   customer.ID.String(),
		"email": customer.Email,
		"name":  customer.GetFullName(),
		"role":  "customer",
		"iat":   time.Now().Unix(),
		"exp":   time.Now().Add(s.expiresIn).Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.secretKey))
}

// ValidateToken validates a JWT token and returns customer info
func (s *AuthService) ValidateToken(tokenString string) (*models.Customer, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(s.secretKey), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		customerIDStr, ok := claims["sub"].(string)
		if !ok {
			return nil, fmt.Errorf("invalid customer ID in token")
		}

		customerID, err := uuid.Parse(customerIDStr)
		if err != nil {
			return nil, fmt.Errorf("invalid customer ID format")
		}

		customer, err := s.customerRepo.GetByID(context.Background(), customerID)
		if err != nil {
			return nil, fmt.Errorf("customer not found")
		}

		return customer, nil
	}

	return nil, fmt.Errorf("invalid token")
}
