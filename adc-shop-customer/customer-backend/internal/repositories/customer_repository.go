package repositories

import (
	"context"
	"fmt"

	"customer-backend/internal/models"
	"customer-backend/pkg/logger"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// CustomerRepository interface defines customer data access methods
type CustomerRepository interface {
	Create(ctx context.Context, customer *models.Customer) error
	GetByID(ctx context.Context, id uuid.UUID) (*models.Customer, error)
	GetByEmail(ctx context.Context, email string) (*models.Customer, error)
	Update(ctx context.Context, customer *models.Customer) error
	Delete(ctx context.Context, id uuid.UUID) error
	List(ctx context.Context, limit, offset int) ([]*models.Customer, error)
	GetByProvider(ctx context.Context, provider, providerID string) (*models.Customer, error)
}

// customerRepository implements CustomerRepository interface
type customerRepository struct {
	db     *gorm.DB
	logger *logger.Logger
}

// NewCustomerRepository creates a new customer repository
func NewCustomerRepository(db *gorm.DB, logger *logger.Logger) CustomerRepository {
	return &customerRepository{
		db:     db,
		logger: logger,
	}
}

// Create creates a new customer in the database
func (r *customerRepository) Create(ctx context.Context, customer *models.Customer) error {
	if err := r.db.WithContext(ctx).Create(customer).Error; err != nil {
		r.logger.WithError(err).Error("Failed to create customer")
		return fmt.Errorf("failed to create customer: %w", err)
	}
	return nil
}

// GetByID retrieves a customer by ID
func (r *customerRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.Customer, error) {
	var customer models.Customer
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&customer).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("customer not found")
		}
		r.logger.WithError(err).Error("Failed to get customer by ID")
		return nil, fmt.Errorf("failed to get customer: %w", err)
	}
	return &customer, nil
}

// GetByEmail retrieves a customer by email
func (r *customerRepository) GetByEmail(ctx context.Context, email string) (*models.Customer, error) {
	var customer models.Customer
	if err := r.db.WithContext(ctx).Where("email = ?", email).First(&customer).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("customer not found")
		}
		r.logger.WithError(err).Error("Failed to get customer by email")
		return nil, fmt.Errorf("failed to get customer: %w", err)
	}
	return &customer, nil
}

// Update updates a customer in the database
func (r *customerRepository) Update(ctx context.Context, customer *models.Customer) error {
	if err := r.db.WithContext(ctx).Save(customer).Error; err != nil {
		r.logger.WithError(err).Error("Failed to update customer")
		return fmt.Errorf("failed to update customer: %w", err)
	}
	return nil
}

// Delete soft deletes a customer from the database
func (r *customerRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&models.Customer{}, id).Error; err != nil {
		r.logger.WithError(err).Error("Failed to delete customer")
		return fmt.Errorf("failed to delete customer: %w", err)
	}
	return nil
}

// List retrieves a list of customers with pagination
func (r *customerRepository) List(ctx context.Context, limit, offset int) ([]*models.Customer, error) {
	var customers []*models.Customer
	query := r.db.WithContext(ctx).Model(&models.Customer{})
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}
	
	if err := query.Find(&customers).Error; err != nil {
		r.logger.WithError(err).Error("Failed to list customers")
		return nil, fmt.Errorf("failed to list customers: %w", err)
	}
	
	return customers, nil
}

// GetByProvider retrieves a customer by OAuth provider and provider ID
func (r *customerRepository) GetByProvider(ctx context.Context, provider, providerID string) (*models.Customer, error) {
	var customer models.Customer
	if err := r.db.WithContext(ctx).Where("provider = ? AND provider_id = ?", provider, providerID).First(&customer).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("customer not found")
		}
		r.logger.WithError(err).Error("Failed to get customer by provider")
		return nil, fmt.Errorf("failed to get customer: %w", err)
	}
	return &customer, nil
}
