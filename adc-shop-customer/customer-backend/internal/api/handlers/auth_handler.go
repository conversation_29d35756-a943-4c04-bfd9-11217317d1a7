package handlers

import (
	"net/http"

	"customer-backend/internal/services"
	"customer-backend/internal/types"
	"customer-backend/pkg/logger"

	"github.com/gin-gonic/gin"
)

// AuthHandler handles authentication-related HTTP requests
type AuthHandler struct {
	authService *services.AuthService
	logger      *logger.Logger
}

// NewAuthHandler creates a new authentication handler
func NewAuthHandler(authService *services.AuthService, logger *logger.Logger) *AuthHandler {
	return &AuthHandler{
		authService: authService,
		logger:      logger,
	}
}

// Login handles customer login requests
func (h *AuthHandler) Login(c *gin.Context) {
	var req types.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid login request")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid request data"))
		return
	}

	response, err := h.authService.Login(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Login failed")
		c.JSON(http.StatusUnauthorized, types.CreateErrorResponse("Invalid credentials"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// Register handles customer registration requests
func (h *AuthHandler) Register(c *gin.Context) {
	var req types.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid registration request")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid request data"))
		return
	}

	response, err := h.authService.Register(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Registration failed")
		if err.Error() == "customer with this email already exists" {
			c.JSON(http.StatusConflict, types.CreateErrorResponse("Email already exists"))
		} else {
			c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Registration failed"))
		}
		return
	}

	c.JSON(http.StatusCreated, response)
}

// CreateOAuthCustomer handles OAuth customer creation/login
func (h *AuthHandler) CreateOAuthCustomer(c *gin.Context) {
	var req types.OAuthCustomerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid OAuth customer request")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid request data"))
		return
	}

	response, err := h.authService.CreateOAuthCustomer(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("OAuth customer creation failed")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("OAuth authentication failed"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// Logout handles customer logout requests
func (h *AuthHandler) Logout(c *gin.Context) {
	// For JWT tokens, logout is typically handled client-side by removing the token
	// Here we can implement token blacklisting if needed in the future

	response := types.LogoutResponse{
		Success: true,
		Message: "Logged out successfully",
	}

	c.JSON(http.StatusOK, response)
}

// GetCurrentCustomer returns the current authenticated customer's information
func (h *AuthHandler) GetCurrentCustomer(c *gin.Context) {
	// Get customer ID from middleware context
	customerIDStr, exists := c.Get("customer_id")
	if !exists {
		h.logger.Error("Customer ID not found in context")
		c.JSON(http.StatusUnauthorized, types.CreateErrorResponse("Authentication required"))
		return
	}

	customerID := customerIDStr.(string)

	// For now, return basic customer info
	// In a full implementation, you'd fetch from the database
	response := types.CustomerData{
		ID:   customerID,
		Role: "customer",
	}

	c.JSON(http.StatusOK, types.CreateCustomerResponse(response, "Customer information retrieved", nil))
}

// RefreshToken handles token refresh requests
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var req types.RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid refresh token request")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid request data"))
		return
	}

	// TODO: Implement refresh token logic
	// For now, return an error as refresh tokens are not implemented
	c.JSON(http.StatusNotImplemented, types.CreateErrorResponse("Refresh token not implemented"))
}

// ChangePassword handles password change requests
func (h *AuthHandler) ChangePassword(c *gin.Context) {
	var req types.ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid change password request")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid request data"))
		return
	}

	// TODO: Implement password change logic
	// For now, return an error as this feature is not implemented
	c.JSON(http.StatusNotImplemented, types.CreateErrorResponse("Change password not implemented"))
}

// ForgotPassword handles forgot password requests
func (h *AuthHandler) ForgotPassword(c *gin.Context) {
	var req types.ForgotPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid forgot password request")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid request data"))
		return
	}

	// TODO: Implement forgot password logic
	// For now, return a success message
	response := types.ForgotPasswordResponse{
		Success: true,
		Message: "Password reset instructions sent to your email",
	}

	c.JSON(http.StatusOK, response)
}

// ResetPassword handles password reset requests
func (h *AuthHandler) ResetPassword(c *gin.Context) {
	var req types.ResetPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid reset password request")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid request data"))
		return
	}

	// TODO: Implement password reset logic
	// For now, return an error as this feature is not implemented
	c.JSON(http.StatusNotImplemented, types.CreateErrorResponse("Password reset not implemented"))
}
