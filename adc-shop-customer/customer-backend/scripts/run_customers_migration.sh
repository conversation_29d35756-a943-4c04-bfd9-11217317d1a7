#!/bin/bash

# Script to run the customers table migration
# This creates the customers table and inserts test data

set -e

echo "🔄 Running customers table migration..."

# Database connection parameters
DB_HOST=${DB_HOST:-"localhost"}
DB_PORT=${DB_PORT:-"5432"}
DB_USER=${DB_USER:-"postgres"}
DB_NAME=${DB_NAME:-"restaurant_db"}

# Check if PGPASSWORD is set
if [ -z "$PGPASSWORD" ]; then
    echo "⚠️  PGPASSWORD environment variable is not set"
    echo "Please set it with: export PGPASSWORD=your_password"
    exit 1
fi

# Run the migration
echo "📊 Creating customers table..."
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f migrations/005_create_customers_table.sql

if [ $? -eq 0 ]; then
    echo "✅ Customers table migration completed successfully!"
    echo ""
    echo "📝 Test customers created:"
    echo "   - <EMAIL> (password: password)"
    echo "   - <EMAIL> (password: password)"
    echo "   - <EMAIL> (password: password)"
    echo ""
    echo "🔐 You can now use these credentials to test authentication"
else
    echo "❌ Migration failed!"
    exit 1
fi
